import 'package:flutter/material.dart';
import '../models/leave_balance.dart';
import '../models/leave_request.dart';
import '../services/mock_data_service.dart';
import '../services/holiday_service.dart';

class EmployeeProvider with ChangeNotifier {
  List<LeaveBalance> _leaveBalances = [];
  List<LeaveRequest> _leaveRequests = [];
  double _availableLeaves = 0.0;
  Map<String, int> _holidaysStatistics = {};
  bool _isLoading = false;
  String? _errorMessage;

  List<LeaveBalance> get leaveBalances => _leaveBalances;
  List<LeaveRequest> get leaveRequests => _leaveRequests;
  double get availableLeaves => _availableLeaves;
  Map<String, int> get holidaysStatistics => _holidaysStatistics;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> loadEmployeeData([String? employeeId]) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // جلب بيانات الإجازات من API أو البيانات التجريبية
      final holidaysResult = await HolidayService.getMyHolidays();

      if (holidaysResult['success'] == true) {
        _leaveRequests = holidaysResult['holidays'] ?? [];
        _availableLeaves = (holidaysResult['available_leaves'] ?? 0.0).toDouble();

        // جلب الإحصائيات
        _holidaysStatistics = await HolidayService.getHolidaysStatistics();

        // جلب أرصدة الإجازات (البيانات التجريبية)
        if (employeeId != null) {
          _leaveBalances = MockDataService.getLeaveBalances(employeeId);
        }
      } else {
        _errorMessage = holidaysResult['message'] ?? 'فشل في جلب بيانات الإجازات';

        // استخدام البيانات التجريبية كـ fallback
        if (employeeId != null) {
          _leaveBalances = MockDataService.getLeaveBalances(employeeId);
          _leaveRequests = MockDataService.getLeaveRequests(employeeId);
          _availableLeaves = 25.0; // رصيد تجريبي
        }
      }
    } catch (e) {
      _errorMessage = 'خطأ في جلب البيانات: ${e.toString()}';

      // استخدام البيانات التجريبية في حالة الخطأ
      if (employeeId != null) {
        _leaveBalances = MockDataService.getLeaveBalances(employeeId);
        _leaveRequests = MockDataService.getLeaveRequests(employeeId);
        _availableLeaves = 25.0;
      }
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> submitLeaveRequest({
    required String employeeId,
    required String leaveType,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
  }) async {
    _isLoading = true;
    notifyListeners();

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    // Calculate number of days
    final numberOfDays = endDate.difference(startDate).inDays + 1;

    // Create new leave request with new format
    final newRequest = LeaveRequest(
      id: 'REQ${DateTime.now().millisecondsSinceEpoch}',
      name: reason,
      status: 'draft',
      holidayStatusId: {'id': 1, 'name': leaveType},
      dateFrom: startDate,
      dateTo: endDate,
      durationDisplay: numberOfDays.toDouble(),
      createdAt: DateTime.now(),
      reason: reason,
      employeeName: 'الموظف الحالي',
      employeeId: employeeId,
    );

    // Add to the list
    _leaveRequests.insert(0, newRequest);

    _isLoading = false;
    notifyListeners();
    
    return true; // Simulate successful submission
  }

  List<String> getAvailableLeaveTypes() {
    return MockDataService.getLeaveTypes();
  }

  int getTotalRemainingDays() {
    return _leaveBalances.fold(0, (sum, balance) => sum + balance.remainingDays);
  }

  int getTotalUsedDays() {
    return _leaveBalances.fold(0, (sum, balance) => sum + balance.usedDays);
  }

  /// جلب الإجازات حسب الحالة
  List<LeaveRequest> getLeaveRequestsByStatus(String status) {
    return _leaveRequests.where((request) => request.status == status).toList();
  }

  /// جلب آخر الإجازات
  List<LeaveRequest> getRecentLeaveRequests({int limit = 5}) {
    final sortedRequests = List<LeaveRequest>.from(_leaveRequests);
    sortedRequests.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedRequests.take(limit).toList();
  }

  /// إعادة تحميل البيانات
  Future<void> refreshData([String? employeeId]) async {
    await loadEmployeeData(employeeId);
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
