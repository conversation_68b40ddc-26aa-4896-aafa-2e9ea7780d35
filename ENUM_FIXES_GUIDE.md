# دليل إصلاح أخطاء Enum والنماذج

## المشكلة الأصلية

كان هناك خطأ في الكود:
```
error: There's no constant named 'approved' in 'LeaveStatus'. (undefined_enum_constant)
```

## السبب

تم تحديث نموذج `LeaveRequest` ليتوافق مع API الجديد، مما أدى إلى:
1. تغيير قيم `LeaveStatus` enum
2. تغيير أسماء الحقول في النموذج
3. تغيير طريقة الوصول إلى البيانات

## الإصلاحات المطبقة

### 1. تحديث `LeaveStatus` enum

#### القديم:
```dart
enum LeaveStatus { pending, approved, rejected }
```

#### الجديد:
```dart
enum LeaveStatus { 
  draft,      // مسودة
  confirm,    // مؤكد
  refuse,     // مرفوض
  validate1,  // موافقة أولى
  validate,   // موافق نهائياً
  cancel      // ملغي
}
```

### 2. تحديث الحقول في `LeaveRequest`

#### القديم:
```dart
final String leaveType;
final DateTime startDate;
final DateTime endDate;
final int numberOfDays;
final LeaveStatus status;
final DateTime requestDate;
final String? approverComments;
```

#### الجديد:
```dart
final String name;
final String status; // String بدلاً من enum
final Map<String, dynamic> holidayStatusId;
final DateTime dateFrom;
final DateTime dateTo;
final double durationDisplay;
final DateTime createdAt;
final String employeeName;
final String employeeId;
```

### 3. إصلاح الملفات المتأثرة

#### `lib/widgets/leave_history_card.dart`
- تحديث switch statement لاستخدام `request.leaveStatus` بدلاً من `request.status`
- استخدام `request.statusDisplayName` للنص المعروض
- تحديث المراجع للحقول:
  - `leaveType` → `leaveTypeName`
  - `startDate` → `dateFrom`
  - `endDate` → `dateTo`
  - `numberOfDays` → `durationDisplay.toInt()`
  - `requestDate` → `createdAt`
- إزالة `approverComments` وإضافة معلومات الموظف

#### `lib/widgets/recent_requests_card.dart`
- نفس التحديثات المطبقة على `leave_history_card.dart`

### 4. إضافة دوال مساعدة في `LeaveRequest`

```dart
// Helper getters
String get leaveTypeName => holidayStatusId['name'] ?? '';
int get leaveTypeId => holidayStatusId['id'] ?? 0;

LeaveStatus get leaveStatus {
  switch (status.toLowerCase()) {
    case 'draft': return LeaveStatus.draft;
    case 'confirm': return LeaveStatus.confirm;
    case 'refuse': return LeaveStatus.refuse;
    case 'validate1': return LeaveStatus.validate1;
    case 'validate': return LeaveStatus.validate;
    case 'cancel': return LeaveStatus.cancel;
    default: return LeaveStatus.draft;
  }
}

String get statusDisplayName {
  switch (leaveStatus) {
    case LeaveStatus.draft: return 'مسودة';
    case LeaveStatus.confirm: return 'مؤكد';
    case LeaveStatus.refuse: return 'مرفوض';
    case LeaveStatus.validate1: return 'موافقة أولى';
    case LeaveStatus.validate: return 'موافق';
    case LeaveStatus.cancel: return 'ملغي';
  }
}
```

## الحالات الجديدة المدعومة

### الألوان والأيقونات حسب الحالة:

| الحالة | النص العربي | اللون | الأيقونة |
|--------|-------------|-------|----------|
| `validate` | موافق | أخضر | ✅ |
| `refuse` | مرفوض | أحمر | ❌ |
| `confirm` | مؤكد | برتقالي | ⏰ |
| `validate1` | موافقة أولى | أزرق | ⏳ |
| `cancel` | ملغي | رمادي | 🚫 |
| `draft` | مسودة | أصفر | ✏️ |

## التحقق من الإصلاحات

تم التحقق من:
- ✅ عدم وجود أخطاء في التجميع
- ✅ تحديث جميع المراجع للحقول القديمة
- ✅ إضافة دعم للحالات الجديدة
- ✅ الحفاظ على التوافق مع API الجديد

## ملاحظات مهمة

1. **التوافق العكسي**: النموذج الجديد يدعم البيانات من API والبيانات التجريبية
2. **معالجة الأخطاء**: إضافة قيم افتراضية للحقول المفقودة
3. **المرونة**: استخدام دوال مساعدة للوصول إلى البيانات بطريقة آمنة
4. **الأداء**: تحسين طريقة عرض البيانات في واجهة المستخدم

## الخطوات التالية

1. اختبار التطبيق مع البيانات الجديدة
2. التأكد من عمل جميع الشاشات بشكل صحيح
3. إضافة المزيد من التحقق من صحة البيانات إذا لزم الأمر
4. تحديث الاختبارات إذا كانت موجودة
