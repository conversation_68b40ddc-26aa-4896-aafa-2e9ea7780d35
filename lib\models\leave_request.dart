enum LeaveStatus {
  draft,      // مسودة
  confirm,    // مؤكد
  refuse,     // مرفوض
  validate1,  // موافقة أولى
  validate,   // موافق نهائياً
  cancel      // ملغي
}

class LeaveRequest {
  final String id;
  final String name;
  final String status;
  final Map<String, dynamic> holidayStatusId;
  final DateTime dateFrom;
  final DateTime dateTo;
  final double durationDisplay;
  final DateTime createdAt;
  final String reason;
  final String employeeName;
  final String employeeId;

  LeaveRequest({
    required this.id,
    required this.name,
    required this.status,
    required this.holidayStatusId,
    required this.dateFrom,
    required this.dateTo,
    required this.durationDisplay,
    required this.createdAt,
    required this.reason,
    required this.employeeName,
    required this.employeeId,
  });

  factory LeaveRequest.fromJson(Map<String, dynamic> json) {
    return LeaveRequest(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      status: json['status'] ?? '',
      holidayStatusId: json['holiday_status_id'] ?? {},
      dateFrom: DateTime.tryParse(json['date_from'] ?? '') ?? DateTime.now(),
      dateTo: DateTime.tryParse(json['date_to'] ?? '') ?? DateTime.now(),
      durationDisplay: (json['duration_display'] ?? 0).toDouble(),
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      reason: json['reason'] ?? '',
      employeeName: json['employee_name'] ?? '',
      employeeId: json['employee_ID'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'status': status,
      'holiday_status_id': holidayStatusId,
      'date_from': dateFrom.toIso8601String(),
      'date_to': dateTo.toIso8601String(),
      'duration_display': durationDisplay,
      'created_at': createdAt.toIso8601String(),
      'reason': reason,
      'employee_name': employeeName,
      'employee_ID': employeeId,
    };
  }

  // Helper getters
  String get leaveTypeName => holidayStatusId['name'] ?? '';
  int get leaveTypeId => holidayStatusId['id'] ?? 0;

  LeaveStatus get leaveStatus {
    switch (status.toLowerCase()) {
      case 'draft':
        return LeaveStatus.draft;
      case 'confirm':
        return LeaveStatus.confirm;
      case 'refuse':
        return LeaveStatus.refuse;
      case 'validate1':
        return LeaveStatus.validate1;
      case 'validate':
        return LeaveStatus.validate;
      case 'cancel':
        return LeaveStatus.cancel;
      default:
        return LeaveStatus.draft;
    }
  }

  String get statusDisplayName {
    switch (leaveStatus) {
      case LeaveStatus.draft:
        return 'مسودة';
      case LeaveStatus.confirm:
        return 'مؤكد';
      case LeaveStatus.refuse:
        return 'مرفوض';
      case LeaveStatus.validate1:
        return 'موافقة أولى';
      case LeaveStatus.validate:
        return 'موافق';
      case LeaveStatus.cancel:
        return 'ملغي';
    }
  }
}
