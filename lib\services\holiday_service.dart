import '../models/leave_request.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/mock_data_service.dart';

class HolidayService {
  /// جلب قائمة الإجازات للموظف الحالي
  static Future<Map<String, dynamic>> getMyHolidays() async {
    final authService = AuthService();
    final sessionId = await authService.getSessionId();
    final database = await authService.getDatabase();

    // إذا كان لدينا session_id و database، استخدم API الحقيقي
    if (sessionId != null && database != null && database.isNotEmpty) {
      try {
        final result = await ApiService.getEmployeeHolidays(
          sessionId: sessionId,
          database: database,
        );

        if (result['success'] == true) {
          final data = result['data'];
          final holidaysList = data['holidays'] as List<dynamic>? ?? [];
          
          // تحويل البيانات إلى نماذج LeaveRequest
          final holidays = holidaysList
              .map((holiday) => LeaveRequest.fromJson(holiday))
              .toList();

          return {
            'success': true,
            'holidays': holidays,
            'available_leaves': data['available_leaves'] ?? 0.0,
            'count': data['count'] ?? 0,
          };
        } else {
          return {
            'success': false,
            'message': result['message'] ?? 'فشل في جلب بيانات الإجازات',
            'requires_login': result['requires_login'] ?? false,
          };
        }
      } catch (e) {
        print('Error in getMyHolidays: $e');
        // في حالة حدوث خطأ، استخدم البيانات التجريبية كـ fallback
        return _getMockHolidays();
      }
    } else {
      // استخدام البيانات التجريبية
      return _getMockHolidays();
    }
  }

  /// الحصول على البيانات التجريبية للإجازات
  static Map<String, dynamic> _getMockHolidays() {
    final mockHolidays = MockDataService.getLeaveRequests();
    return {
      'success': true,
      'holidays': mockHolidays,
      'available_leaves': 25.0, // رصيد تجريبي
      'count': mockHolidays.length,
    };
  }

  /// جلب رصيد الإجازات المتاح
  static Future<double> getAvailableLeaves() async {
    final result = await getMyHolidays();
    if (result['success'] == true) {
      return (result['available_leaves'] ?? 0.0).toDouble();
    }
    return 0.0;
  }

  /// جلب عدد الإجازات
  static Future<int> getHolidaysCount() async {
    final result = await getMyHolidays();
    if (result['success'] == true) {
      return result['count'] ?? 0;
    }
    return 0;
  }

  /// جلب الإجازات حسب الحالة
  static Future<List<LeaveRequest>> getHolidaysByStatus(String status) async {
    final result = await getMyHolidays();
    if (result['success'] == true) {
      final holidays = result['holidays'] as List<LeaveRequest>;
      return holidays.where((holiday) => holiday.status == status).toList();
    }
    return [];
  }

  /// جلب آخر الإجازات المطلوبة
  static Future<List<LeaveRequest>> getRecentHolidays({int limit = 5}) async {
    final result = await getMyHolidays();
    if (result['success'] == true) {
      final holidays = result['holidays'] as List<LeaveRequest>;
      // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
      holidays.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return holidays.take(limit).toList();
    }
    return [];
  }

  /// إحصائيات الإجازات
  static Future<Map<String, int>> getHolidaysStatistics() async {
    final result = await getMyHolidays();
    if (result['success'] == true) {
      final holidays = result['holidays'] as List<LeaveRequest>;
      
      final stats = <String, int>{
        'total': holidays.length,
        'draft': 0,
        'confirm': 0,
        'validate': 0,
        'refuse': 0,
        'cancel': 0,
      };

      for (final holiday in holidays) {
        final status = holiday.status.toLowerCase();
        if (stats.containsKey(status)) {
          stats[status] = stats[status]! + 1;
        }
      }

      return stats;
    }
    return {
      'total': 0,
      'draft': 0,
      'confirm': 0,
      'validate': 0,
      'refuse': 0,
      'cancel': 0,
    };
  }
}
