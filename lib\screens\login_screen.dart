import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/auth_provider.dart';
import '../utils/app_colors.dart';
import '../config/api_config.dart';

class LoginScreen extends StatefulWidget {
  final VoidCallback? onLoginSuccess;

  const LoginScreen({super.key, this.onLoginSuccess});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _databaseController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  String? _selectedUsername;
  bool _useApiLogin = false;

  @override
  void dispose() {
    _databaseController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // استخدام قاعدة البيانات الافتراضية إذا كان مفعل في التكوين
      String? databaseToUse;
      if (_useApiLogin) {
        if (ApiConfig.useDefaultDatabase) {
          databaseToUse = ApiConfig.defaultDatabase;
        } else {
          databaseToUse = _databaseController.text.trim();
        }
      }

      final success = await authProvider.login(
        _usernameController.text.trim(),
        _passwordController.text,
        database: databaseToUse,
      );

      if (success && mounted) {
        widget.onLoginSuccess?.call();
      } else if (mounted) {
        final errorMessage = authProvider.errorMessage ?? 'اسم المستخدم أو كلمة المرور غير صحيحة';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _selectUsername(String? username) {
    if (username != null) {
      setState(() {
        _selectedUsername = username;
        _usernameController.text = username;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo and title
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(60),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    FontAwesomeIcons.userTie,
                    size: 60,
                    color: AppColors.white,
                  ),
                ),
                const SizedBox(height: 32),

                Text(
                  'نظام إدارة الموظفين',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'مرحباً بك، يرجى تسجيل الدخول للمتابعة',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 48),

                // Login form
                Card(
                  elevation: 8,
                  shadowColor: AppColors.shadow,
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Toggle between API and Demo login
                          Row(
                            children: [
                              Switch(
                                value: _useApiLogin,
                                onChanged: (value) {
                                  setState(() {
                                    _useApiLogin = value;
                                    if (!value) {
                                      _databaseController.clear();
                                    }
                                  });
                                },
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _useApiLogin ? 'استخدام API الحقيقي' : 'الوضع التجريبي',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Database field (only for API login and when not using default)
                          if (_useApiLogin && !ApiConfig.useDefaultDatabase) ...[
                            TextFormField(
                              controller: _databaseController,
                              decoration: const InputDecoration(
                                labelText: 'اسم قاعدة البيانات',
                                prefixIcon: Icon(FontAwesomeIcons.database),
                                hintText: 'مثال: my_database',
                              ),
                              validator: (value) {
                                if (_useApiLogin && !ApiConfig.useDefaultDatabase && (value == null || value.isEmpty)) {
                                  return 'يرجى إدخال اسم قاعدة البيانات';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                          ],

                          // عرض اسم قاعدة البيانات الافتراضية
                          if (_useApiLogin && ApiConfig.useDefaultDatabase) ...[
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: AppColors.primary.withOpacity(0.3)),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.database,
                                    color: AppColors.primary,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'قاعدة البيانات: ${ApiConfig.defaultDatabase}',
                                    style: TextStyle(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                          ],

                          // Username dropdown for demo (only in demo mode)
                          if (!_useApiLogin)
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, _) {
                                final usernames = authProvider
                                    .getAvailableUsernames();
                                return DropdownButtonFormField<String>(
                                  value: _selectedUsername,
                                  decoration: const InputDecoration(
                                    labelText: 'اختر اسم المستخدم (للتجربة)',
                                    prefixIcon: Icon(FontAwesomeIcons.user),
                                  ),
                                  items: usernames.map((username) {
                                    return DropdownMenuItem(
                                      value: username,
                                      child: Text(username),
                                    );
                                  }).toList(),
                                  onChanged: _selectUsername,
                                );
                              },
                            ),
                          if (!_useApiLogin) const SizedBox(height: 16),

                          // Username field
                          TextFormField(
                            controller: _usernameController,
                            decoration: const InputDecoration(
                              labelText: 'اسم المستخدم',
                              prefixIcon: Icon(FontAwesomeIcons.user),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال اسم المستخدم';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Password field
                          TextFormField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            decoration: InputDecoration(
                              labelText: 'كلمة المرور',
                              prefixIcon: const Icon(FontAwesomeIcons.lock),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? FontAwesomeIcons.eyeSlash
                                      : FontAwesomeIcons.eye,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال كلمة المرور';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 32),

                          // Login button
                          Consumer<AuthProvider>(
                            builder: (context, authProvider, _) {
                              return ElevatedButton(
                                onPressed: authProvider.isLoading
                                    ? null
                                    : _login,
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                ),
                                child: authProvider.isLoading
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                AppColors.white,
                                              ),
                                        ),
                                      )
                                    : const Text(
                                        'تسجيل الدخول',
                                        style: TextStyle(fontSize: 16),
                                      ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Info section
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.info.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.info.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            FontAwesomeIcons.circleInfo,
                            color: AppColors.info,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _useApiLogin ? 'تسجيل الدخول عبر API:' : 'بيانات تجريبية للاختبار:',
                            style: TextStyle(
                              color: AppColors.info,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _useApiLogin
                          ? 'تأكد من إدخال اسم قاعدة البيانات وبيانات المستخدم الصحيحة'
                          : 'كلمة المرور لجميع المستخدمين: admin123 أو password123',
                        style: TextStyle(color: AppColors.info, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
