class ApiConfig {
  // إعدادات الخادم - يجب تغييرها حسب بيئة العمل
  static const String baseUrl =
      'http://192.168.0.139:8069'; // أو رابط الخادم الخاص بك
  static const String loginEndpoint = '/api/login';
  static const String employeeEndpoint = '/api/employee/me';

  // إعدادات قاعدة البيانات
  // قم بتغيير 'employee_db' إلى اسم قاعدة البيانات الخاصة بك
  static const String defaultDatabase = 'employee_db'; // اسم قاعدة البيانات الافتراضي

  // إذا كان true: سيتم استخدام defaultDatabase تلقائياً ولن يظهر حقل إدخال قاعدة البيانات
  // إذا كان false: سيظهر حقل لإدخال اسم قاعدة البيانات في كل مرة
  static const bool useDefaultDatabase = true;

  // إعدادات الاتصال
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);

  // رؤوس HTTP الافتراضية
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // إعدادات التطبيق
  static const bool enableDebugMode = true; // تفعيل وضع التطوير
  static const bool enableMockData = true; // تفعيل البيانات التجريبية

  // الحصول على رابط كامل للـ endpoint
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }

  // التحقق من صحة إعدادات الخادم
  static bool isValidServerUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }
}
