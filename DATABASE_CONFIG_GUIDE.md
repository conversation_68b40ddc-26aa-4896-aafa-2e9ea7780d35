# دليل تكوين قاعدة البيانات الافتراضية

## التغييرات المطبقة

تم تطبيق التحديثات التالية لتثبيت اسم قاعدة البيانات في الكود بدلاً من إدخالها في كل مرة:

### 1. إضافة إعدادات قاعدة البيانات في `lib/config/api_config.dart`

```dart
// إعدادات قاعدة البيانات
static const String defaultDatabase = 'employee_db'; // اسم قاعدة البيانات الافتراضي
static const bool useDefaultDatabase = true; // استخدام قاعدة البيانات الافتراضية
```

### 2. تحديث شاشة تسجيل الدخول `lib/screens/login_screen.dart`

- **إخفاء حقل قاعدة البيانات**: عندما يكون `useDefaultDatabase = true`، لن يظهر حقل إدخال قاعدة البيانات
- **عرض اسم قاعدة البيانات**: يتم عرض اسم قاعدة البيانات المستخدمة في مربع معلومات
- **استخدام تلقائي**: يتم استخدام `defaultDatabase` تلقائياً عند تسجيل الدخول

## كيفية التخصيص

### لتغيير اسم قاعدة البيانات:
1. افتح ملف `lib/config/api_config.dart`
2. غيّر قيمة `defaultDatabase`:
```dart
static const String defaultDatabase = 'اسم_قاعدة_البيانات_الخاصة_بك';
```

### لتفعيل/إلغاء تفعيل الاستخدام التلقائي:
```dart
// للاستخدام التلقائي (لن يظهر حقل إدخال قاعدة البيانات)
static const bool useDefaultDatabase = true;

// لإظهار حقل إدخال قاعدة البيانات في كل مرة
static const bool useDefaultDatabase = false;
```

## المزايا

✅ **سهولة الاستخدام**: لا حاجة لإدخال اسم قاعدة البيانات في كل مرة
✅ **مرونة التكوين**: يمكن تفعيل/إلغاء تفعيل الميزة بسهولة
✅ **وضوح المعلومات**: عرض اسم قاعدة البيانات المستخدمة للمستخدم
✅ **سهولة الصيانة**: تغيير اسم قاعدة البيانات من مكان واحد

## طريقة الاستخدام الجديدة

1. **تشغيل التطبيق**
2. **تفعيل "استخدام API الحقيقي"**
3. **لن يظهر حقل قاعدة البيانات** (إذا كان `useDefaultDatabase = true`)
4. **سيظهر مربع معلومات** يوضح اسم قاعدة البيانات المستخدمة
5. **إدخال اسم المستخدم وكلمة المرور**
6. **تسجيل الدخول**

## ملاحظات مهمة

- تأكد من أن اسم قاعدة البيانات في `defaultDatabase` يطابق اسم قاعدة البيانات الفعلية على الخادم
- يمكن تغيير الإعدادات في أي وقت دون الحاجة لإعادة تثبيت التطبيق
- الميزة تعمل فقط مع "استخدام API الحقيقي" وليس مع الوضع التجريبي
