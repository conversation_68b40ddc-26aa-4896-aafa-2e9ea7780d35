# مثال على استخدام بيانات الإجازات الجديدة

## في شاشة لوحة التحكم

```dart
// في DashboardScreen
Consumer<EmployeeProvider>(
  builder: (context, employeeProvider, child) {
    return Column(
      children: [
        // عرض الرصيد المتاح
        Card(
          child: ListTile(
            leading: Icon(Icons.calendar_today),
            title: Text('الرصيد المتاح'),
            subtitle: Text('${employeeProvider.availableLeaves} يوم'),
          ),
        ),
        
        // عرض إحصائيات الإجازات
        Card(
          child: Column(
            children: [
              Text('إحصائيات الإجازات'),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      Text('${employeeProvider.holidaysStatistics['total'] ?? 0}'),
                      Text('إجمالي'),
                    ],
                  ),
                  Column(
                    children: [
                      Text('${employeeProvider.holidaysStatistics['validate'] ?? 0}'),
                      Text('موافق عليها'),
                    ],
                  ),
                  Column(
                    children: [
                      Text('${employeeProvider.holidaysStatistics['draft'] ?? 0}'),
                      Text('مسودة'),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // عرض آخر الإجازات
        Card(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.all(16),
                child: Text('آخر الإجازات', style: Theme.of(context).textTheme.titleMedium),
              ),
              ...employeeProvider.getRecentLeaveRequests(limit: 3).map(
                (request) => ListTile(
                  leading: Icon(_getStatusIcon(request.status)),
                  title: Text(request.name),
                  subtitle: Text('${request.dateFrom.day}/${request.dateFrom.month} - ${request.dateTo.day}/${request.dateTo.month}'),
                  trailing: Chip(
                    label: Text(request.statusDisplayName),
                    backgroundColor: _getStatusColor(request.status),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  },
)
```

## في شاشة الإجازات

```dart
// في LeaveBalanceScreen
Consumer<EmployeeProvider>(
  builder: (context, employeeProvider, child) {
    if (employeeProvider.isLoading) {
      return Center(child: CircularProgressIndicator());
    }
    
    if (employeeProvider.errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(employeeProvider.errorMessage!),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => employeeProvider.refreshData(),
              child: Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }
    
    return ListView(
      children: [
        // رصيد الإجازات
        Card(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('رصيد الإجازات', style: Theme.of(context).textTheme.titleLarge),
                SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.account_balance_wallet, color: Colors.green),
                    SizedBox(width: 8),
                    Text('${employeeProvider.availableLeaves} يوم متاح'),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        // قائمة الإجازات
        ...employeeProvider.leaveRequests.map(
          (request) => Card(
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: _getStatusColor(request.status),
                child: Icon(_getStatusIcon(request.status), color: Colors.white),
              ),
              title: Text(request.name),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('النوع: ${request.leaveTypeName}'),
                  Text('المدة: ${request.durationDisplay} أيام'),
                  Text('من ${_formatDate(request.dateFrom)} إلى ${_formatDate(request.dateTo)}'),
                ],
              ),
              trailing: Chip(
                label: Text(request.statusDisplayName),
                backgroundColor: _getStatusColor(request.status),
              ),
              onTap: () => _showRequestDetails(context, request),
            ),
          ),
        ),
      ],
    );
  },
)
```

## دوال مساعدة

```dart
IconData _getStatusIcon(String status) {
  switch (status.toLowerCase()) {
    case 'validate':
      return Icons.check_circle;
    case 'confirm':
      return Icons.pending;
    case 'draft':
      return Icons.edit;
    case 'refuse':
      return Icons.cancel;
    case 'cancel':
      return Icons.block;
    default:
      return Icons.help;
  }
}

Color _getStatusColor(String status) {
  switch (status.toLowerCase()) {
    case 'validate':
      return Colors.green;
    case 'confirm':
      return Colors.orange;
    case 'draft':
      return Colors.blue;
    case 'refuse':
      return Colors.red;
    case 'cancel':
      return Colors.grey;
    default:
      return Colors.grey;
  }
}

String _formatDate(DateTime date) {
  return '${date.day}/${date.month}/${date.year}';
}

void _showRequestDetails(BuildContext context, LeaveRequest request) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(request.name),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('النوع: ${request.leaveTypeName}'),
          Text('السبب: ${request.reason}'),
          Text('المدة: ${request.durationDisplay} أيام'),
          Text('تاريخ البداية: ${_formatDate(request.dateFrom)}'),
          Text('تاريخ النهاية: ${_formatDate(request.dateTo)}'),
          Text('الحالة: ${request.statusDisplayName}'),
          Text('تاريخ الطلب: ${_formatDate(request.createdAt)}'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('إغلاق'),
        ),
      ],
    ),
  );
}
```

## تحميل البيانات عند بدء التطبيق

```dart
// في main.dart أو في initState للشاشة الرئيسية
@override
void initState() {
  super.initState();
  WidgetsBinding.instance.addPostFrameCallback((_) {
    final employeeProvider = Provider.of<EmployeeProvider>(context, listen: false);
    employeeProvider.loadEmployeeData();
  });
}
```
