import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../models/leave_request.dart';
import '../utils/app_colors.dart';

class LeaveHistoryCard extends StatelessWidget {
  final LeaveRequest leaveRequest;

  const LeaveHistoryCard({
    super.key,
    required this.leaveRequest,
  });

  @override
  Widget build(BuildContext context) {
    Color statusColor;
    IconData statusIcon;
    String statusText;
    Color cardBorderColor;

    // استخدام statusDisplayName من النموذج الجديد
    statusText = leaveRequest.statusDisplayName;

    switch (leaveRequest.leaveStatus) {
      case LeaveStatus.validate:
        statusColor = AppColors.success;
        statusIcon = FontAwesomeIcons.circleCheck;
        cardBorderColor = AppColors.success.withOpacity(0.3);
        break;
      case LeaveStatus.refuse:
        statusColor = AppColors.error;
        statusIcon = FontAwesomeIcons.circleXmark;
        cardBorderColor = AppColors.error.withOpacity(0.3);
        break;
      case LeaveStatus.confirm:
        statusColor = AppColors.warning;
        statusIcon = FontAwesomeIcons.clock;
        cardBorderColor = AppColors.warning.withOpacity(0.3);
        break;
      case LeaveStatus.validate1:
        statusColor = AppColors.info;
        statusIcon = FontAwesomeIcons.hourglassHalf;
        cardBorderColor = AppColors.info.withOpacity(0.3);
        break;
      case LeaveStatus.cancel:
        statusColor = AppColors.error;
        statusIcon = FontAwesomeIcons.ban;
        cardBorderColor = AppColors.error.withOpacity(0.3);
        break;
      case LeaveStatus.draft:
      default:
        statusColor = AppColors.warning;
        statusIcon = FontAwesomeIcons.edit;
        cardBorderColor = AppColors.warning.withOpacity(0.3);
        break;
    }

    IconData leaveTypeIcon;
    // استخدام leaveTypeName من النموذج الجديد
    switch (leaveRequest.leaveTypeName) {
      case 'إجازة سنوية':
        leaveTypeIcon = FontAwesomeIcons.umbrellaBeach;
        break;
      case 'إجازة مرضية':
        leaveTypeIcon = FontAwesomeIcons.userDoctor;
        break;
      case 'إجازة طارئة':
        leaveTypeIcon = FontAwesomeIcons.triangleExclamation;
        break;
      case 'إجازة أمومة':
        leaveTypeIcon = FontAwesomeIcons.baby;
        break;
      case 'إجازة حج':
        leaveTypeIcon = FontAwesomeIcons.kaaba;
        break;
      default:
        leaveTypeIcon = FontAwesomeIcons.calendar;
    }

    return Card(
      elevation: 4,
      shadowColor: statusColor.withOpacity(0.2),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: cardBorderColor, width: 1),
        ),
        child: Column(
          children: [
            // Header with status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.05),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: FaIcon(
                      leaveTypeIcon,
                      color: statusColor,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          leaveRequest.leaveTypeName.isNotEmpty
                            ? leaveRequest.leaveTypeName
                            : leaveRequest.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'طلب رقم: ${leaveRequest.id}',
                          style: const TextStyle(
                            fontSize: 11,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FaIcon(
                          statusIcon,
                          color: statusColor,
                          size: 12,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          statusText,
                          style: TextStyle(
                            color: statusColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Date range and duration
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.calendarDays,
                          'تاريخ البداية',
                          DateFormat('dd/MM/yyyy').format(leaveRequest.dateFrom),
                          AppColors.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.calendarCheck,
                          'تاريخ النهاية',
                          DateFormat('dd/MM/yyyy').format(leaveRequest.dateTo),
                          AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.clock,
                          'المدة',
                          '${leaveRequest.durationDisplay.toInt()} يوم',
                          AppColors.accent,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.paperPlane,
                          'تاريخ الطلب',
                          DateFormat('dd/MM/yyyy').format(leaveRequest.createdAt),
                          AppColors.info,
                        ),
                      ),
                    ],
                  ),

                  // Reason
                  if (leaveRequest.reason.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.grey50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.border),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              FaIcon(
                                FontAwesomeIcons.comment,
                                color: AppColors.textSecondary,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'السبب:',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Text(
                            leaveRequest.reason,
                            style: const TextStyle(
                              fontSize: 13,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // معلومات إضافية عن الموظف
                  if (leaveRequest.employeeName.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.info.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.info.withOpacity(0.2)),
                      ),
                      child: Row(
                        children: [
                          FaIcon(
                            FontAwesomeIcons.user,
                            color: AppColors.info,
                            size: 14,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'الموظف: ${leaveRequest.employeeName}',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppColors.info,
                            ),
                          ),
                          if (leaveRequest.employeeId.isNotEmpty) ...[
                            const SizedBox(width: 8),
                            Text(
                              '(${leaveRequest.employeeId})',
                              style: TextStyle(
                                fontSize: 11,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          FaIcon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
