# دليل دمج API الإجازات

## التحديثات المطبقة

تم دمج API الإجازات الجديد `/api/holiday/my_requests` مع نظام إدارة الموظفين. إليك التفاصيل:

### 1. تحديث نماذج البيانات

#### `lib/models/leave_request.dart`
- تم تحديث نموذج `LeaveRequest` ليتوافق مع تنسيق API الجديد
- إضافة حالات جديدة للإجازات: `draft`, `confirm`, `validate`, `refuse`, `cancel`
- إضافة دوال مساعدة لتحويل الحالات إلى نصوص عربية

#### `lib/models/employee.dart`
- إضافة حقول جديدة لبيانات الإجازات:
  - `availableLeaves`: الرصيد المتاح من الإجازات
  - `holidays`: قائمة الإجازات
  - `holidaysCount`: عدد الإجازات

### 2. إضافة خدمات جديدة

#### `lib/services/holiday_service.dart`
خدمة شاملة لإدارة الإجازات تتضمن:
- `getMyHolidays()`: جلب جميع إجازات الموظف
- `getAvailableLeaves()`: جلب الرصيد المتاح
- `getHolidaysByStatus()`: جلب الإجازات حسب الحالة
- `getRecentHolidays()`: جلب آخر الإجازات
- `getHolidaysStatistics()`: إحصائيات الإجازات

#### تحديث `lib/services/api_service.dart`
- إضافة دالة `getEmployeeHolidays()` لجلب بيانات الإجازات من API

#### تحديث `lib/services/auth_service.dart`
- دمج بيانات الإجازات مع بيانات الموظف عند تسجيل الدخول
- جلب بيانات الإجازات تلقائياً مع بيانات الموظف

### 3. تحديث التكوين

#### `lib/config/api_config.dart`
```dart
static const String holidaysEndpoint = '/api/holiday/my_requests';
```

### 4. تحديث مقدمي البيانات (Providers)

#### `lib/providers/employee_provider.dart`
- إضافة متغيرات جديدة للإجازات
- تحديث `loadEmployeeData()` لاستخدام `HolidayService`
- إضافة دوال مساعدة للإجازات

### 5. تحديث البيانات التجريبية

#### `lib/services/mock_data_service.dart`
- تحديث `getLeaveRequests()` لتتوافق مع تنسيق API الجديد
- إضافة بيانات تجريبية متنوعة للإجازات

## تنسيق API المدعوم

### الطلب (Request)
```http
GET /api/holiday/my_requests
Headers:
  Cookie: session_id=<session_id>
  Content-Type: application/json
```

### الاستجابة (Response)
```json
{
  "success": true,
  "count": 4,
  "available_leaves": 25.0,
  "holidays": [
    {
      "id": 1,
      "name": "إجازة نهاية السنة",
      "status": "validate",
      "holiday_status_id": {
        "id": 1,
        "name": "إجازة سنوية"
      },
      "date_from": "2024-12-20",
      "date_to": "2024-12-25",
      "duration_display": 5.0,
      "created_at": "2024-11-15T10:00:00",
      "reason": "إجازة نهاية السنة",
      "employee_name": "أحمد علي السعيد",
      "employee_ID": "EMP002"
    }
  ]
}
```

## المزايا الجديدة

✅ **دمج تلقائي**: بيانات الإجازات تُجلب تلقائياً مع بيانات الموظف
✅ **دعم مزدوج**: API حقيقي + بيانات تجريبية كـ fallback
✅ **إحصائيات شاملة**: عرض إحصائيات مفصلة للإجازات
✅ **حالات متقدمة**: دعم جميع حالات الإجازات من النظام
✅ **أداء محسن**: جلب البيانات بطلب واحد

## كيفية الاستخدام

### في الكود
```dart
// جلب إجازات الموظف
final holidaysResult = await HolidayService.getMyHolidays();

// جلب الرصيد المتاح
final availableLeaves = await HolidayService.getAvailableLeaves();

// جلب الإحصائيات
final stats = await HolidayService.getHolidaysStatistics();
```

### في واجهة المستخدم
- بيانات الإجازات متاحة في `EmployeeProvider`
- يمكن الوصول إليها من أي شاشة باستخدام `Provider.of<EmployeeProvider>(context)`

## ملاحظات مهمة

1. **التوافق العكسي**: النظام يعمل مع البيانات التجريبية إذا لم يكن API متاحاً
2. **معالجة الأخطاء**: معالجة شاملة للأخطاء مع رسائل واضحة
3. **الأداء**: البيانات تُحفظ محلياً لتحسين الأداء
4. **الأمان**: استخدام session_id للمصادقة

## الخطوات التالية

1. اختبار API مع الخادم الحقيقي
2. تحديث واجهات المستخدم لعرض البيانات الجديدة
3. إضافة ميزات إضافية مثل تقديم طلبات إجازة جديدة
4. تحسين معالجة الأخطاء والتحقق من صحة البيانات
