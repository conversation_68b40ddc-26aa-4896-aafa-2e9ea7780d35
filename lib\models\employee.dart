class Employee {
  final String id;
  final String name;
  final String email;
  final String department;
  final String position;
  final String phoneNumber;
  final DateTime joinDate;
  final String employeeId;
  final String manager;
  final double salary;
  final String profileImage;

  // New fields from API
  final String? intId;
  final List<dynamic>? departmentId; // [id, name]
  final List<dynamic>? parentId; // [id, name] - Manager
  final List<dynamic>? coachId; // [id, name] - Coach
  final bool? connectedWithComp;
  final String? nationalNumber;

  // بيانات الإجازات
  final double? availableLeaves; // الرصيد المتاح من الإجازات
  final List<dynamic>? holidays; // قائمة الإجازات
  final int? holidaysCount; // عدد الإجازات

  Employee({
    required this.id,
    required this.name,
    required this.email,
    required this.department,
    required this.position,
    required this.phoneNumber,
    required this.joinDate,
    required this.employeeId,
    required this.manager,
    required this.salary,
    this.profileImage = '',
    // New optional fields
    this.intId,
    this.departmentId,
    this.parentId,
    this.coachId,
    this.connectedWithComp,
    this.nationalNumber,
    // بيانات الإجازات الجديدة
    this.availableLeaves,
    this.holidays,
    this.holidaysCount,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'] ?? json['int_id']?.toString() ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      department:
          json['department'] ??
          (json['department_id'] != null &&
                  json['department_id'] is List &&
                  json['department_id'].length > 1
              ? json['department_id'][1]
              : ''),
      position: json['position'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      joinDate: json['joinDate'] != null
          ? DateTime.parse(json['joinDate'])
          : DateTime.now(),
      employeeId: json['employeeId'] ?? json['int_id']?.toString() ?? '',
      manager:
          json['manager'] ??
          (json['parent_id'] != null &&
                  json['parent_id'] is List &&
                  json['parent_id'].length > 1
              ? json['parent_id'][1]
              : ''),
      salary: json['salary']?.toDouble() ?? 0.0,
      profileImage: json['profileImage'] ?? '',
      // New fields from API
      intId: json['int_id']?.toString(),
      departmentId: json['department_id'],
      parentId: json['parent_id'],
      coachId: json['coach_id'],
      connectedWithComp: json['connected_with_comp'],
      nationalNumber: json['national_number']?.toString(),
      // بيانات الإجازات
      availableLeaves: json['available_leaves']?.toDouble(),
      holidays: json['holidays'],
      holidaysCount: json['holidays_count'],
    );
  }

  // Factory constructor specifically for API response
  factory Employee.fromApiResponse(Map<String, dynamic> json) {
    // معالجة connected_with_comp - قد يكون String أو bool أو int
    bool? connectedWithCompValue;
    final connectedValue = json['connected_with_comp'];
    if (connectedValue != null) {
      if (connectedValue is bool) {
        connectedWithCompValue = connectedValue;
      } else if (connectedValue is String) {
        // تحويل String إلى bool
        connectedWithCompValue =
            connectedValue.toLowerCase() == 'true' ||
            connectedValue == '1' ||
            connectedValue == 'yes' ||
            (int.tryParse(connectedValue) ?? 0) > 0;
      } else if (connectedValue is int) {
        connectedWithCompValue = connectedValue > 0;
      }
    }

    return Employee(
      id: json['int_id']?.toString() ?? '',
      name: json['name'] ?? '',
      email: '', // Not provided by API, will be empty
      department:
          json['department_id'] != null &&
              json['department_id'] is List &&
              json['department_id'].length > 1
          ? json['department_id'][1]
          : '',
      position: '', // Not provided by API, will be empty
      phoneNumber: '', // Not provided by API, will be empty
      joinDate: DateTime.now(), // Not provided by API, use current date
      employeeId: json['int_id']?.toString() ?? '',
      manager:
          json['parent_id'] != null &&
              json['parent_id'] is List &&
              json['parent_id'].length > 1
          ? json['parent_id'][1]
          : '',
      salary: 0.0, // Not provided by API
      profileImage: '',
      // New fields from API
      intId: json['int_id']?.toString(),
      departmentId: json['department_id'],
      parentId: json['parent_id'],
      coachId: json['coach_id'],
      connectedWithComp: connectedWithCompValue,
      nationalNumber: json['national_number']?.toString(),
      // بيانات الإجازات من API
      availableLeaves: json['available_leaves']?.toDouble(),
      holidays: json['holidays'],
      holidaysCount: json['holidays_count'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'department': department,
      'position': position,
      'phoneNumber': phoneNumber,
      'joinDate': joinDate.toIso8601String(),
      'employeeId': employeeId,
      'manager': manager,
      'salary': salary,
      'profileImage': profileImage,
      // New fields
      'int_id': intId,
      'department_id': departmentId,
      'parent_id': parentId,
      'coach_id': coachId,
      'connected_with_comp': connectedWithComp,
      'national_number': nationalNumber,
      // بيانات الإجازات
      'available_leaves': availableLeaves,
      'holidays': holidays,
      'holidays_count': holidaysCount,
    };
  }

  // Helper getters for easier access to nested data
  String get departmentName => departmentId != null && departmentId!.length > 1
      ? departmentId![1]
      : department;
  String get managerName =>
      parentId != null && parentId!.length > 1 ? parentId![1] : manager;
  String get coachName =>
      coachId != null && coachId!.length > 1 ? coachId![1] : '';
}
